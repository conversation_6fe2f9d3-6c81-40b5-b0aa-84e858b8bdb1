<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 200px;
      padding: 15px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 15px;
    }
    
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
    
    .subtitle {
      font-size: 12px;
      color: #666;
      margin: 5px 0 0 0;
    }
    
    .toggle-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 15px 0;
    }
    
    .toggle-label {
      font-size: 14px;
      color: #333;
    }
    
    .toggle-switch {
      position: relative;
      width: 50px;
      height: 24px;
      background-color: #ccc;
      border-radius: 12px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    .toggle-switch.active {
      background-color: #4CAF50;
    }
    
    .toggle-slider {
      position: absolute;
      top: 2px;
      left: 2px;
      width: 20px;
      height: 20px;
      background-color: white;
      border-radius: 50%;
      transition: transform 0.3s;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    .toggle-switch.active .toggle-slider {
      transform: translateX(26px);
    }
    
    .status {
      text-align: center;
      font-size: 12px;
      color: #666;
      margin-top: 10px;
    }
    
    .status.enabled {
      color: #4CAF50;
    }
    
    .status.disabled {
      color: #f44336;
    }
    
    .footer {
      text-align: center;
      margin-top: 15px;
      padding-top: 10px;
      border-top: 1px solid #eee;
      font-size: 11px;
      color: #999;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1 class="title">Portal UBIF</h1>
    <p class="subtitle">Dark Mode Extension</p>
  </div>
  
  <div class="toggle-container">
    <span class="toggle-label">Dark Mode</span>
    <div class="toggle-switch" id="darkModeToggle">
      <div class="toggle-slider"></div>
    </div>
  </div>
  
  <div class="status" id="status">Loading...</div>
  
  <div class="footer">
    Only active on portal.ubif.net
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
