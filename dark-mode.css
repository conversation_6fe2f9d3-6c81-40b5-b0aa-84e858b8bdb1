/* Dark Mode CSS for portal.ubif.net */

/* Hide the extension styles by default - they will be applied via JavaScript */
.dark-mode-extension {
  display: none;
}

/* When dark mode is enabled, apply these styles */
body.dark-mode-enabled {
  background-color: #1a1a1a !important;
  color: #e0e0e0 !important;
}

body.dark-mode-enabled * {
  background-color: inherit !important;
  color: inherit !important;
  border-color: #444 !important;
}

/* Common elements styling */
body.dark-mode-enabled div,
body.dark-mode-enabled section,
body.dark-mode-enabled article,
body.dark-mode-enabled aside,
body.dark-mode-enabled nav,
body.dark-mode-enabled header,
body.dark-mode-enabled footer,
body.dark-mode-enabled main {
  background-color: #1a1a1a !important;
  color: #e0e0e0 !important;
}

/* Form elements */
body.dark-mode-enabled input,
body.dark-mode-enabled textarea,
body.dark-mode-enabled select,
body.dark-mode-enabled button {
  background-color: #2d2d2d !important;
  color: #e0e0e0 !important;
  border: 1px solid #444 !important;
}

body.dark-mode-enabled input:focus,
body.dark-mode-enabled textarea:focus,
body.dark-mode-enabled select:focus {
  border-color: #666 !important;
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(100, 100, 100, 0.3) !important;
}

/* Buttons */
body.dark-mode-enabled button {
  background-color: #3d3d3d !important;
  color: #e0e0e0 !important;
  border: 1px solid #555 !important;
}

body.dark-mode-enabled button:hover {
  background-color: #4d4d4d !important;
}

/* Links */
body.dark-mode-enabled a {
  color: #66b3ff !important;
}

body.dark-mode-enabled a:visited {
  color: #b366ff !important;
}

body.dark-mode-enabled a:hover {
  color: #80c7ff !important;
}

/* Tables */
body.dark-mode-enabled table {
  background-color: #1a1a1a !important;
  color: #e0e0e0 !important;
}

body.dark-mode-enabled th,
body.dark-mode-enabled td {
  background-color: #2d2d2d !important;
  color: #e0e0e0 !important;
  border-color: #444 !important;
}

body.dark-mode-enabled th {
  background-color: #3d3d3d !important;
}

/* Cards and containers */
body.dark-mode-enabled .card,
body.dark-mode-enabled .container,
body.dark-mode-enabled .panel,
body.dark-mode-enabled .box {
  background-color: #2d2d2d !important;
  color: #e0e0e0 !important;
  border-color: #444 !important;
}

/* Navigation and menus */
body.dark-mode-enabled nav,
body.dark-mode-enabled .navbar,
body.dark-mode-enabled .menu {
  background-color: #2d2d2d !important;
  color: #e0e0e0 !important;
}

/* Modals and overlays */
body.dark-mode-enabled .modal,
body.dark-mode-enabled .overlay,
body.dark-mode-enabled .popup {
  background-color: #1a1a1a !important;
  color: #e0e0e0 !important;
  border-color: #444 !important;
}

/* Code blocks */
body.dark-mode-enabled code,
body.dark-mode-enabled pre {
  background-color: #0d1117 !important;
  color: #f0f6fc !important;
  border-color: #30363d !important;
}

/* Scrollbars */
body.dark-mode-enabled ::-webkit-scrollbar {
  background-color: #2d2d2d !important;
}

body.dark-mode-enabled ::-webkit-scrollbar-thumb {
  background-color: #555 !important;
  border-radius: 4px !important;
}

body.dark-mode-enabled ::-webkit-scrollbar-thumb:hover {
  background-color: #666 !important;
}

/* Images - reduce brightness slightly */
body.dark-mode-enabled img {
  filter: brightness(0.8) !important;
}

/* Specific overrides for white backgrounds */
body.dark-mode-enabled [style*="background-color: white"],
body.dark-mode-enabled [style*="background-color: #fff"],
body.dark-mode-enabled [style*="background-color: #ffffff"],
body.dark-mode-enabled [style*="background: white"],
body.dark-mode-enabled [style*="background: #fff"],
body.dark-mode-enabled [style*="background: #ffffff"] {
  background-color: #1a1a1a !important;
}

/* Specific overrides for black text */
body.dark-mode-enabled [style*="color: black"],
body.dark-mode-enabled [style*="color: #000"],
body.dark-mode-enabled [style*="color: #000000"] {
  color: #e0e0e0 !important;
}
