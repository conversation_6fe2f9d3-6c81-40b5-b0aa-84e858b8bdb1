// Popup script for Portal UBIF Dark Mode extension

document.addEventListener('DOMContentLoaded', function() {
  const toggle = document.getElementById('darkModeToggle');
  const status = document.getElementById('status');
  
  // Check if we're on the correct domain
  chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
    const currentTab = tabs[0];
    
    if (!currentTab.url.includes('portal.ubif.net')) {
      status.textContent = 'Not on portal.ubif.net';
      status.className = 'status disabled';
      toggle.style.opacity = '0.5';
      toggle.style.pointerEvents = 'none';
      return;
    }
    
    // Get current dark mode status
    chrome.tabs.sendMessage(currentTab.id, { action: 'getDarkModeStatus' }, function(response) {
      if (chrome.runtime.lastError) {
        status.textContent = 'Please refresh the page';
        status.className = 'status disabled';
        return;
      }
      
      if (response && response.enabled) {
        toggle.classList.add('active');
        status.textContent = 'Dark mode enabled';
        status.className = 'status enabled';
      } else {
        toggle.classList.remove('active');
        status.textContent = 'Dark mode disabled';
        status.className = 'status disabled';
      }
    });
    
    // Handle toggle click
    toggle.addEventListener('click', function() {
      chrome.tabs.sendMessage(currentTab.id, { action: 'toggleDarkMode' }, function(response) {
        if (chrome.runtime.lastError) {
          status.textContent = 'Error: Please refresh the page';
          status.className = 'status disabled';
          return;
        }
        
        if (response && response.success) {
          // Toggle the UI
          toggle.classList.toggle('active');
          
          if (toggle.classList.contains('active')) {
            status.textContent = 'Dark mode enabled';
            status.className = 'status enabled';
          } else {
            status.textContent = 'Dark mode disabled';
            status.className = 'status disabled';
          }
        }
      });
    });
  });
});
