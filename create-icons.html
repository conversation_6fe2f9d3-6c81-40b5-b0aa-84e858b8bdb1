<!DOCTYPE html>
<html>
<head>
    <title>Create Extension Icons</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .icon-container { display: inline-block; margin: 10px; text-align: center; }
    </style>
</head>
<body>
    <h1>Portal UBIF Dark Mode Extension Icons</h1>
    <p>Right-click each icon and "Save image as..." to save them to the icons folder.</p>
    
    <div class="icon-container">
        <canvas id="icon16" width="16" height="16"></canvas>
        <br>icon16.png
    </div>
    
    <div class="icon-container">
        <canvas id="icon32" width="32" height="32"></canvas>
        <br>icon32.png
    </div>
    
    <div class="icon-container">
        <canvas id="icon48" width="48" height="48"></canvas>
        <br>icon48.png
    </div>
    
    <div class="icon-container">
        <canvas id="icon128" width="128" height="128"></canvas>
        <br>icon128.png
    </div>

    <script>
        function createIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // Dark background
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(0, 0, size, size);
            
            // Light circle (moon-like)
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.3;
            
            ctx.fillStyle = '#e0e0e0';
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Dark crescent (to make it look like a moon)
            ctx.fillStyle = '#1a1a1a';
            ctx.beginPath();
            ctx.arc(centerX + radius * 0.3, centerY - radius * 0.1, radius * 0.8, 0, 2 * Math.PI);
            ctx.fill();
            
            // Add a small star
            if (size >= 32) {
                ctx.fillStyle = '#66b3ff';
                ctx.beginPath();
                const starX = centerX + radius * 1.2;
                const starY = centerY - radius * 1.2;
                const starSize = size * 0.05;
                ctx.arc(starX, starY, starSize, 0, 2 * Math.PI);
                ctx.fill();
            }
        }
        
        // Create all icons
        createIcon('icon16', 16);
        createIcon('icon32', 32);
        createIcon('icon48', 48);
        createIcon('icon128', 128);
        
        // Add download functionality
        document.querySelectorAll('canvas').forEach(canvas => {
            canvas.addEventListener('contextmenu', function(e) {
                // Allow right-click to save
            });
            
            canvas.addEventListener('click', function() {
                const link = document.createElement('a');
                link.download = canvas.id + '.png';
                link.href = canvas.toDataURL();
                link.click();
            });
        });
    </script>
</body>
</html>
