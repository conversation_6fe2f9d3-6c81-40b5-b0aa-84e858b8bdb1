# Portal UBIF Dark Mode Extension

A Chrome extension that adds dark mode functionality to the portal.ubif.net website.

## Features

- Toggle dark mode on/off for portal.ubif.net
- Persistent settings (remembers your preference)
- Clean, modern dark theme
- Works with dynamically loaded content
- Easy-to-use popup interface

## Installation

### Method 1: Load as Unpacked Extension (Development)

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" by toggling the switch in the top right corner
3. Click "Load unpacked" button
4. Select the folder containing this extension
5. The extension should now appear in your extensions list

### Method 2: Create Icons (Optional)

The extension references icon files that you can create:
- `icons/icon16.png` (16x16 pixels)
- `icons/icon32.png` (32x32 pixels) 
- `icons/icon48.png` (48x48 pixels)
- `icons/icon128.png` (128x128 pixels)

You can create simple dark mode icons or use placeholder images. The extension will work without these icons, but they improve the visual experience.

## Usage

1. Navigate to https://portal.ubif.net
2. Click the extension icon in your Chrome toolbar
3. Toggle the "Dark Mode" switch to enable/disable dark mode
4. Your preference will be saved automatically

## Files Structure

- `manifest.json` - Extension configuration
- `content.js` - Main script that applies dark mode
- `dark-mode.css` - Dark mode styles
- `popup.html` - Extension popup interface
- `popup.js` - Popup functionality
- `icons/` - Extension icons (optional)

## How It Works

The extension uses:
- **Content Scripts**: Inject CSS and JavaScript into portal.ubif.net pages
- **Storage API**: Remember user preferences
- **Popup Interface**: Easy toggle control
- **Dynamic Content Handling**: Works with single-page applications

## Customization

You can modify `dark-mode.css` to adjust the dark theme colors and styling to your preferences. The current theme uses:
- Background: `#1a1a1a` (dark gray)
- Text: `#e0e0e0` (light gray)
- Accents: `#2d2d2d` (medium gray)
- Links: `#66b3ff` (light blue)

## Troubleshooting

- **Extension not working**: Make sure you're on portal.ubif.net and refresh the page
- **Popup shows "Please refresh the page"**: Refresh the portal.ubif.net tab
- **Dark mode not applying**: Check that the extension has permission for portal.ubif.net

## Development

To modify the extension:
1. Make changes to the files
2. Go to `chrome://extensions/`
3. Click the refresh icon on the extension card
4. Refresh any portal.ubif.net tabs to see changes
