// Content script for Portal UBIF Dark Mode extension

(function() {
  'use strict';

  // Check if dark mode is enabled
  function isDarkModeEnabled() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['darkModeEnabled'], (result) => {
        resolve(result.darkModeEnabled !== false); // Default to true
      });
    });
  }

  // Apply dark mode
  function applyDarkMode() {
    document.body.classList.add('dark-mode-enabled');
    
    // Also apply to dynamically loaded content
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Force inherit styles on new elements
              const elements = node.querySelectorAll('*');
              elements.forEach((el) => {
                if (document.body.classList.contains('dark-mode-enabled')) {
                  // Let CSS handle the styling
                }
              });
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // Remove dark mode
  function removeDarkMode() {
    document.body.classList.remove('dark-mode-enabled');
  }

  // Toggle dark mode
  function toggleDarkMode() {
    if (document.body.classList.contains('dark-mode-enabled')) {
      removeDarkMode();
      chrome.storage.sync.set({ darkModeEnabled: false });
    } else {
      applyDarkMode();
      chrome.storage.sync.set({ darkModeEnabled: true });
    }
  }

  // Listen for messages from popup
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'toggleDarkMode') {
      toggleDarkMode();
      sendResponse({ success: true });
    } else if (request.action === 'getDarkModeStatus') {
      const isEnabled = document.body.classList.contains('dark-mode-enabled');
      sendResponse({ enabled: isEnabled });
    }
  });

  // Initialize dark mode based on stored preference
  isDarkModeEnabled().then((enabled) => {
    if (enabled) {
      applyDarkMode();
    }
  });

  // Handle page navigation (for SPAs)
  let currentUrl = location.href;
  const urlObserver = new MutationObserver(() => {
    if (location.href !== currentUrl) {
      currentUrl = location.href;
      // Reapply dark mode after navigation
      isDarkModeEnabled().then((enabled) => {
        if (enabled) {
          setTimeout(() => applyDarkMode(), 100);
        }
      });
    }
  });

  urlObserver.observe(document.body, {
    childList: true,
    subtree: true
  });

})();
